#ifndef _UART_H_
#define _UART_H_

#include "zf_common_headfile.h"

// UART2数据接收相关变量声明
extern int8 storageU2[4];   // 存储数据部分（4个字节）
extern int8 rx_art2[8];     // 存储完整的8字节数据帧
extern uint8 rx_data2;       // 当前接收到的字节

// 全局变量声明
extern int detection_flag;     // 检测标志：1-检测到物体，0-未检测到
extern int center_x;           // 矩形中心X轴坐标
extern int center_y;           // 矩形中心Y轴坐标
extern int reserved_byte;      // 保留字节
extern int uart2_data_received; // UART2数据接收标志：1-收到数据，0-未收到数据

// 函数声明
void uart2_init_with_interrupt(void);
void uart2_rx_interrupt_new_handler(void);
void uart2_rx_art(uint8 data);
void rt1064_storageU2(void);

#endif
