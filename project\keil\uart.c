#include "zf_common_headfile.h"
#include "uart.h"
// UART2数据接收相关变量定义
int8 storageU2[4];   // 存储数据部分（4个字节）：data0（检测标志）、data1（x轴偏移量）、data2（测距低字节）、data3（测距高字节）
int8 rx_art2[8];     // 存储完整的8字节数据帧：头帧2字节 + 数据部分4字节 + 尾帧2字节
int8 rx_data2 = 0;   // 当前接收到的字节

// 全局变量定义
int detection_flag = 0;     // 检测标志：1-检测到物体，0-未检测到
int center_x = 0;           // 矩形中心X轴坐标
int center_y = 0;           // 矩形中心Y轴坐标
int reserved_byte = 0;      // 保留字节

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     UART2初始化并启用接收中断
// 参数说明     void
// 返回参数     void
// 使用示例     uart2_init_with_interrupt();
// 备注信息     使用UART2_TX_B15和UART2_RX_B16引脚，波特率115200
//-------------------------------------------------------------------------------------------------------------------
void uart2_init_with_interrupt(void)
{
    // 初始化UART2，使用B15作为TX，B16作为RX，波特率115200
    uart_init(UART_2, 115200, UART2_TX_B15, UART2_RX_B16);

    // 启用UART2接收中断
    uart_set_interrupt_config(UART_2, UART_INTERRUPT_CONFIG_RX_ENABLE);

    // 设置UART2中断优先级
    interrupt_set_priority(UART2_INT_IRQn, 1);
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     UART2串口中断接收函数
// 参数说明     void
// 返回参数     void
// 使用示例     uart2_rx_interrupt_new_handler();
// 备注信息     在UART2中断服务函数中调用
//-------------------------------------------------------------------------------------------------------------------
void uart2_rx_interrupt_new_handler(void)
{ 
    // 查询接收一个字节，若有数据则存入 rx_data2
    uart_query_byte(UART_2, &rx_data2);
    
    // 调用接收函数，由其内部判断完整帧再调用 rt1064_storageU2()
    uart2_rx_art(rx_data2);
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     UART2数据帧接收状态机（8字节数据帧）
// 参数说明     data            接收到的单个字节数据
// 返回参数     void
// 使用示例     uart2_rx_art(data);
// 备注信息     数据帧格式：0x2B 0x11 + 4字节数据 + 0x5A 0x59
//-------------------------------------------------------------------------------------------------------------------
void uart2_rx_art(uint8 data)
{
    static uint8 stage = 0;
    uint8 i;  // 循环变量，用于清空缓存数组

    switch(stage)
    {
        case 0: // 等待接收头帧1：0x2B
            if(data == 0x2B)
            {
                rx_art2[0] = data;
                stage = 1;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art2[i] = 0x00;
                }
            }
            break;

        case 1: // 等待接收头帧2：0x11
            if(data == 0x11)
            {
                rx_art2[1] = data;
                stage = 2;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art2[i] = 0x00;
                }
            }
            break;

        case 2: // 接收数据部分1：data0（检测标志）
            rx_art2[2] = data;
            stage = 3;
            break;

        case 3: // 接收数据部分2：data1（x轴偏移量）
            rx_art2[3] = data;
            stage = 4;
            break;

        case 4: // 接收数据部分3：data2（测距低字节）
            rx_art2[4] = data;
            stage = 5;
            break;

        case 5: // 接收数据部分4：data3（测距高字节）
            rx_art2[5] = data;
            stage = 6;
            break;

        case 6: // 等待接收尾帧1：0x5A
            if(data == 0x5A)
            {
                rx_art2[6] = data;
                stage = 7;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art2[i] = 0x00;
                }
            }
            break;

        case 7: // 等待接收尾帧2：0x59
            if(data == 0x59)
            {
                rx_art2[7] = data;
                // 完整接收到一帧数据，调用数据处理函数
                rt1064_storageU2();
            }
            // 无论是否接收正确，状态都重置，并清空缓存数组
            stage = 0;
            for(i = 0; i < 8; i++)
            {
                rx_art2[i] = 0x00;
            }
            break;

        default:
            stage = 0;
            for(i = 0; i < 8; i++)
            {
                rx_art2[i] = 0x00;
            }
            break;
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     从UART_2存储数据的函数
// 参数说明     void
// 返回参数     void
// 使用示例     rt1064_storageU2();
// 备注信息     处理接收到的完整数据帧，解析上位机发送的检测数据
//-------------------------------------------------------------------------------------------------------------------
void rt1064_storageU2(void)
{
    // 将接收到的数据部分存入 storageU2 数组中
    storageU2[0] = rx_art2[2]; // 检测标志：1-检测到物体，0-未检测到
    storageU2[1] = rx_art2[3]; // 矩形中心X轴坐标
    storageU2[2] = rx_art2[4]; // 矩形中心Y轴坐标
    storageU2[3] = rx_art2[5]; // 保留字节

    // 更新全局变量，供后续逻辑使用
    detection_flag = storageU2[0];  // 检测标志
    center_x = storageU2[1];        // 中心X坐标
    center_y = storageU2[2];        // 中心Y坐标
    reserved_byte = storageU2[3];   // 保留字节
}
